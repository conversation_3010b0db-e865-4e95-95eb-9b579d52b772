# Heibooky CI/CD Pipeline Setup Guide

This comprehensive guide will help you set up a complete CI/CD pipeline for the Heibooky Django application using GitHub Actions, <PERSON><PERSON>ler for secrets management, and AWS EC2 for deployment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Doppler Setup](#doppler-setup)
3. [GitHub Repository Configuration](#github-repository-configuration)
4. [AWS Infrastructure Setup](#aws-infrastructure-setup)
5. [GitHub Actions Secrets Configuration](#github-actions-secrets-configuration)
6. [Initial Deployment](#initial-deployment)
7. [Testing the Pipeline](#testing-the-pipeline)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before starting, ensure you have:

- GitHub repository with admin access
- AWS account with EC2 and RDS access
- Doppler account (free tier available)
- Domain name for your application (optional but recommended)
- Basic knowledge of Docker, Linux, and CI/CD concepts

## Doppler Setup

### 1. Create Doppler Account and Project

1. Sign up at [doppler.com](https://doppler.com)
2. Create a new project named `heibooky`
3. Create three environments:
   - `dev` (Development)
   - `stg` (Staging)
   - `prd` (Production)

### 2. Configure Environment Variables

For each environment, configure the following secrets in Doppler:

#### Database Configuration
```
DB_NAME=heibooky_[env]
DB_USER=heibooky_user
DB_PASSWORD=[secure_password]
DB_HOST=[rds_endpoint]
DB_PORT=5432
```

#### Django Configuration
```
SECRET_KEY=[django_secret_key_50_chars]
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=[secure_password]
```

#### Redis Configuration
```
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_USERNAME=django_app
REDIS_PASSWORD=[secure_password]
```

#### Email Configuration (Mailgun)
```
MAILGUN_API_KEY=[your_mailgun_api_key]
MAILGUN_DOMAIN=[your_mailgun_domain]
DEFAULT_FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
```

#### AWS Configuration
```
AWS_ACCESS_KEY_ID=[aws_access_key]
AWS_SECRET_ACCESS_KEY=[aws_secret_key]
AWS_STORAGE_BUCKET_NAME=[s3_bucket_name]
AWS_S3_ENDPOINT_URL=[s3_endpoint]
AWS_CDN_URL=[cloudfront_url]
```

#### External API Keys
```
STRIPE_SECRET_KEY=[stripe_secret_key]
IPINFO_TOKEN=[ipinfo_token]
DIGITHERA_BASE_URL=[digithera_url]
DIGITHERA_IDENTIFIER=[digithera_id]
DIGITHERA_API_TOKEN=[digithera_token]
SU_API_BASE_URL=[su_api_url]
SU_API_APP_ID=[su_api_app_id]
SU_API_KEY=[su_api_key]
CGN_API_KEY=[cgn_api_key]
```

#### URLs
```
FRONTEND_URL=https://your-domain.com
ADMIN_URL=https://admin.your-domain.com
SUPPORT_URL=https://support.your-domain.com
```

### 3. Generate Service Tokens

For each environment, generate a service token:

1. Go to your Doppler project
2. Navigate to each environment (dev, stg, prd)
3. Go to "Access" → "Service Tokens"
4. Create a new service token with read access
5. Save these tokens securely - you'll need them for GitHub Actions

## GitHub Repository Configuration

### 1. Repository Secrets

Add the following secrets to your GitHub repository (Settings → Secrets and variables → Actions):

#### Doppler Tokens
```
DOPPLER_TOKEN_DEV=[dev_service_token]
DOPPLER_TOKEN_STG=[staging_service_token]
DOPPLER_TOKEN_PRD=[production_service_token]
```

#### AWS SSH Access
```
AWS_PRIVATE_KEY=[private_key_content]
AWS_HOST_STG=[staging_server_ip]
AWS_HOST_PRD=[production_server_ip]
AWS_USER=ubuntu
```

#### Container Registry
The GitHub Actions workflow uses GitHub Container Registry (ghcr.io) which automatically uses `GITHUB_TOKEN`.

### 2. Environment Protection Rules

1. Go to Settings → Environments
2. Create environments: `staging` and `production`
3. For production environment, add protection rules:
   - Required reviewers (recommended)
   - Deployment branches: only `main`
   - Environment secrets (if needed)

## AWS Infrastructure Setup

### 1. RDS Database Setup

Create a PostgreSQL RDS instance:

```bash
# Using AWS CLI
aws rds create-db-instance \
    --db-instance-identifier heibooky-prod \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --engine-version 15.5 \
    --master-username postgres \
    --manage-master-user-password \
    --allocated-storage 20 \
    --storage-type gp2 \
    --vpc-security-group-ids sg-xxxxxxxxx \
    --db-subnet-group-name default \
    --backup-retention-period 7 \
    --storage-encrypted \
    --deletion-protection \
    --enable-performance-insights

### 2. EC2 Instance Setup

#### Launch EC2 Instance

1. Launch Ubuntu 22.04 LTS instance (t3.medium or larger recommended)
2. Configure security groups:
   - SSH (22) from your IP
   - HTTP (80) from anywhere
   - HTTPS (443) from anywhere
3. Create or use existing key pair
4. Assign Elastic IP (recommended)

#### Initial Server Configuration

SSH into your server and run:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure system for Redis (fix memory overcommit warning)
echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl vm.overcommit_memory=1

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Doppler CLI
curl -Ls https://cli.doppler.com/install.sh | sudo sh

# Create application directory
mkdir -p /opt/heibooky
chown ubuntu:ubuntu /opt/heibooky

# Clone repository
cd /opt/heibooky
git clone https://github.com/Heibooky/Heibooky.git .

# Set up Doppler authentication
doppler login
doppler setup --project heibooky --config prd

# Create necessary directories
mkdir -p /opt/heibooky/backups
mkdir -p /var/log/heibooky
chown -R ubuntu:ubuntu /var/log/heibooky

# Make scripts executable
chmod +x scripts/*.sh
```

### 3. SSL Certificate Setup (Optional)

If using a domain name, set up SSL with Let's Encrypt:

```bash
# Install Certbot
apt install certbot python3-certbot-nginx -y

# Obtain certificate (replace with your domain)
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

## GitHub Actions Secrets Configuration

Ensure all required secrets are configured in your GitHub repository:

### Required Secrets Checklist

- [ ] `DOPPLER_TOKEN_DEV`
- [ ] `DOPPLER_TOKEN_STG`
- [ ] `DOPPLER_TOKEN_PRD`
- [ ] `AWS_PRIVATE_KEY`
- [ ] `AWS_HOST_STG`
- [ ] `AWS_HOST_PRD`
- [ ] `AWS_USER`

### Verify Secrets

You can verify secrets are properly configured by checking the GitHub Actions workflow runs.

## Initial Deployment

### 1. Manual First Deployment

For the first deployment, you may want to deploy manually to ensure everything works:

```bash
# On your EC2 server
cd /opt/heibooky

# Set environment variables
export DOPPLER_TOKEN_PRD=[your_production_token]
export IMAGE_TAG=latest

# Run initial deployment
./scripts/deploy.sh production --force
```

### 2. Verify Deployment

After deployment, verify everything is working:

```bash
# Run health checks
./scripts/health-check.sh --url http://localhost --verbose

# Check container status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs web
```

## Testing the Pipeline

### 1. Test Staging Deployment

1. Create a feature branch
2. Push to `develop` branch
3. Verify staging deployment in GitHub Actions
4. Check staging server

### 2. Test Production Deployment

1. Merge to `main` branch
2. Verify production deployment in GitHub Actions
3. Check production server
4. Verify GitHub release is created

### 3. Test Rollback

```bash
# On production server
./scripts/deploy.sh production --rollback
```

## Monitoring and Maintenance

### 1. Regular Health Checks

Set up a cron job for regular health checks:

```bash
# Add to crontab (crontab -e)
*/5 * * * * /opt/heibooky/scripts/health-check.sh --url http://localhost >> /var/log/heibooky/health.log 2>&1
```

### 2. Log Rotation

Configure log rotation:

```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/heibooky << EOF
/var/log/heibooky/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
}
EOF
```

### 3. Backup Strategy

- Database backups are created automatically during deployments
- Consider setting up automated S3 backups for media files
- Monitor backup storage usage

## Troubleshooting

### Common Issues and Solutions

#### 1. Deployment Fails with "Permission Denied"

**Solution:**
```bash
# Ensure correct permissions
sudo chown -R ubuntu:ubuntu /opt/heibooky
chmod +x scripts/*.sh
```

#### 2. Docker Images Not Pulling

**Solution:**
```bash
# Login to GitHub Container Registry (secure method)
# Use Doppler to manage the token securely
doppler secrets set GITHUB_TOKEN [your_token]
doppler run -- docker login ghcr.io -u USERNAME --password-stdin

#### 3. Database Connection Issues

**Solution:**
```bash
# Check RDS security groups
# Ensure EC2 security group can access RDS on port 5432
# Verify database credentials in Doppler
```

#### 4. SSL Certificate Issues

**Solution:**
```bash
# Renew certificates
sudo certbot renew

# Check certificate status
sudo certbot certificates
```

#### 5. Health Checks Failing

**Solution:**
```bash
# Check container logs
docker-compose -f docker-compose.prod.yml logs web

# Verify all services are running
docker-compose -f docker-compose.prod.yml ps

# Check system resources
df -h
free -h
```

### Getting Help

1. Check GitHub Actions logs for CI/CD issues
2. Check application logs: `docker-compose -f docker-compose.prod.yml logs`
3. Run health checks: `./scripts/health-check.sh --verbose`
4. Check Doppler configuration and tokens
5. Verify AWS security groups and network configuration

### Useful Commands

```bash
# View deployment logs
tail -f /var/log/heibooky/deploy.log

# Check Docker container status
docker-compose -f docker-compose.prod.yml ps

# View application logs
docker-compose -f docker-compose.prod.yml logs -f web

# Restart services
doppler run -- docker-compose -f docker-compose.prod.yml restart

# Update Doppler secrets
doppler secrets set KEY=value

# Manual backup
./scripts/backup.sh production

This setup provides a robust, secure, and scalable CI/CD pipeline for your Heibooky application. Regular monitoring and maintenance will ensure smooth operations.
