# Redis Configuration for Local Development

This document describes the Redis configuration changes made to enable local development without authentication.

## Changes Made

### 1. Redis Configuration (`redis/redis.conf`)
- Created a new Redis configuration file with authentication disabled
- Set `protected-mode no` to allow connections without authentication
- Configured Redis to bind to all interfaces (`bind 0.0.0.0`)
- Disabled persistence for faster development

### 2. Docker Compose Configuration
- Removed Redis password requirements from all services
- Removed Redis ACL configuration
- Simplified Redis healthcheck to use `redis-cli ping`
- Updated environment variables to remove password-related settings

### 3. Django Settings (`heibooky/settings.py`)
- Modified Redis connection configuration to remove authentication
- Simplified `get_redis_url()` function to return URL without credentials
- Removed password file reading logic

### 4. Scripts and Tools
- Created `scripts/redis-entrypoint.sh` for Redis container initialization
- Created `scripts/test_redis_connection.py` for testing Redis connectivity
- Created `docker-compose.override.yml` for local development

## Usage

### Starting the Services
```bash
# Basic startup
docker-compose up -d

# Or with override for development
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
```

### Testing Redis Connection
```bash
# From within the project directory
python scripts/test_redis_connection.py

# Or from within a container
docker-compose exec web python /app/scripts/test_redis_connection.py
```

### Accessing Redis Directly
```bash
# Connect to Redis CLI
docker-compose exec redis redis-cli

# Or if Redis port is exposed (development mode)
redis-cli -h localhost -p 6379
```

## Security Considerations

⚠️ **Important**: This configuration is designed for local development only. For production environments, proper authentication and security measures should be implemented.

## Services That Use Redis

1. **Web Service**: Uses Redis for caching and session storage
2. **Celery Worker**: Uses Redis as message broker
3. **Celery Beat**: Uses Redis as message broker for scheduled tasks
4. **Redis Exporter**: Monitors Redis metrics (authentication removed)

## Environment Variables

The following environment variables are used for Redis configuration:

- `REDIS_HOST`: Redis server hostname (default: redis)
- `REDIS_PORT`: Redis server port (default: 6379)
- `REDIS_DB`: Redis database number (default: 0)

## Troubleshooting

### Connection Issues
1. Ensure Redis container is running: `docker-compose ps redis`
2. Check Redis logs: `docker-compose logs redis`
3. Test connection: `python scripts/test_redis_connection.py`

### Service Issues
1. Check service logs: `docker-compose logs web` or `docker-compose logs celery-worker`
2. Verify environment variables are set correctly
3. Ensure Redis is accessible from within the network
