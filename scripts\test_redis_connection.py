#!/usr/bin/env python3
"""
Redis Connection Test Script
Test Redis connectivity without authentication
"""

import redis
import sys
import os

def test_redis_connection():
    """Test Redis connection without authentication"""
    redis_host = os.getenv('REDIS_HOST', 'localhost')
    redis_port = int(os.getenv('REDIS_PORT', '6379'))
    redis_db = int(os.getenv('REDIS_DB', '0'))
    
    print(f"Testing Redis connection to {redis_host}:{redis_port}, DB: {redis_db}")
    
    try:
        # Create Redis connection without authentication
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        
        # Test basic operations
        print("Testing PING...")
        pong = r.ping()
        print(f"PING response: {pong}")
        
        print("Testing SET/GET...")
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        print(f"GET test_key: {value}")
        
        print("Cleaning up...")
        r.delete('test_key')
        
        print("✅ Redis connection test successful!")
        return True
        
    except redis.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        return False
    except redis.TimeoutError as e:
        print(f"❌ Timeout Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1)
